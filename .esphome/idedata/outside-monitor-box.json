{"build_type": "release", "env_name": "outside-monitor-box", "libsource_dirs": ["/Users/<USER>/ESPHome/outside-monitor/.esphome/build/outside-monitor-box/lib", "/Users/<USER>/ESPHome/outside-monitor/.esphome/build/outside-monitor-box/.piolibdeps/outside-monitor-box", "/Users/<USER>/.platformio/lib"], "defines": ["_POSIX_READER_WRITER_LOCKS", "_GNU_SOURCE", "_GLIBCXX_USE_POSIX_SEMAPHORE", "_GLIBCXX_HAVE_POSIX_SEMAPHORE", "SOC_XTAL_FREQ_MHZ=CONFIG_XTAL_FREQ", "SOC_MMU_PAGE_SIZE=CONFIG_MMU_PAGE_SIZE", "IDF_VER=\"5.4.2\"", "ESP_PLATFORM", "ESP_MDNS_VERSION_NUMBER=\"1.8.2\"", "PLATFORMIO=60118", "ARDUINO_ESP32_DEV", "ESPHOME_LOG_LEVEL=ESPHOME_LOG_LEVEL_DEBUG", "USE_ESP32", "USE_ESP32_FRAMEWORK_ESP_IDF", "USE_ESP32_VARIANT_ESP32", "USE_ESP_IDF"], "includes": {"build": ["/Users/<USER>/ESPHome/outside-monitor/.esphome/build/outside-monitor-box/src", "/Users/<USER>/ESPHome/outside-monitor/.esphome/build/outside-monitor-box/.piolibdeps/outside-monitor-box/ArduinoJson/src", "/Users/<USER>/.platformio/packages/framework-espidf/components/xtensa/esp32/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/xtensa/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/xtensa/deprecated_include", "/Users/<USER>/ESPHome/outside-monitor/.esphome/build/outside-monitor-box/.pioenvs/outside-monitor-box/config", "/Users/<USER>/.platformio/packages/framework-espidf/components/newlib/platform_include", "/Users/<USER>/.platformio/packages/framework-espidf/components/freertos/config/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/freertos/config/include/freertos", "/Users/<USER>/.platformio/packages/framework-espidf/components/freertos/config/xtensa/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/freertos/FreeRTOS-Kernel/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/freertos/FreeRTOS-Kernel/portable/xtensa/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/freertos/FreeRTOS-Kernel/portable/xtensa/include/freertos", "/Users/<USER>/.platformio/packages/framework-espidf/components/freertos/esp_additions/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/include/soc", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/include/soc/esp32", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/dma/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/ldo/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/debug_probe/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/port/esp32", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hw_support/port/esp32/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/heap/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/heap/tlsf", "/Users/<USER>/.platformio/packages/framework-espidf/components/log/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/soc/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/soc/esp32", "/Users/<USER>/.platformio/packages/framework-espidf/components/soc/esp32/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/soc/esp32/register", "/Users/<USER>/.platformio/packages/framework-espidf/components/hal/platform_port/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/hal/esp32/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/hal/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_rom/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_rom/esp32/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_rom/esp32/include/esp32", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_rom/esp32", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_common/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_system/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_system/port/soc", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_system/port/include/private", "/Users/<USER>/.platformio/packages/framework-espidf/components/lwip/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/lwip/include/apps", "/Users/<USER>/.platformio/packages/framework-espidf/components/lwip/include/apps/sntp", "/Users/<USER>/.platformio/packages/framework-espidf/components/lwip/lwip/src/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/lwip/port/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/lwip/port/freertos/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/lwip/port/esp32xx/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/lwip/port/esp32xx/include/arch", "/Users/<USER>/.platformio/packages/framework-espidf/components/lwip/port/esp32xx/include/sys", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_gpio/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_timer/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_pm/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/mbedtls/port/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/mbedtls/mbedtls/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/mbedtls/mbedtls/library", "/Users/<USER>/.platformio/packages/framework-espidf/components/mbedtls/esp_crt_bundle/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/mbedtls/mbedtls/3rdparty/everest/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/mbedtls/mbedtls/3rdparty/p256-m", "/Users/<USER>/.platformio/packages/framework-espidf/components/mbedtls/mbedtls/3rdparty/p256-m/p256-m", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_app_format/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_bootloader_format/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/app_update/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/bootloader_support/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/bootloader_support/bootloader_flash/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_partition/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/efuse/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/efuse/esp32/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_mm/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/spi_flash/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_security/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/pthread/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_gptimer/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_ringbuf/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_uart/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/vfs/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/app_trace/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_event/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/nvs_flash/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_pcnt/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_spi/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_mcpwm/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_ana_cmpr/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_i2s/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/sdmmc/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_sdmmc/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_sdspi/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_sdio/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_dac/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_rmt/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_tsens/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_sdm/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_i2c/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_ledc/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_parlio/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_usb_serial_jtag/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/driver/deprecated", "/Users/<USER>/.platformio/packages/framework-espidf/components/driver/i2c/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/driver/touch_sensor/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/driver/twai/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/driver/touch_sensor/esp32/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_phy/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_phy/esp32/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_vfs_console/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_netif/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/wpa_supplicant/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/wpa_supplicant/port/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/wpa_supplicant/esp_supplicant/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_coex/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_wifi/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_wifi/include/local", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_wifi/wifi_apps/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_wifi/wifi_apps/nan_app/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_gdbstub/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/unity/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/unity/unity/src", "/Users/<USER>/.platformio/packages/framework-espidf/components/cmock/CMock/src", "/Users/<USER>/.platformio/packages/framework-espidf/components/console", "/Users/<USER>/.platformio/packages/framework-espidf/components/http_parser", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp-tls", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp-tls/esp-tls-crypto", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_adc/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_adc/interface", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_adc/esp32/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_adc/deprecated/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_isp/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_cam/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_cam/interface", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_psram/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_jpeg/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_driver_ppa/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_eth/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_hid/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/tcp_transport/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_http_client/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_http_server/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_https_ota/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_https_server/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_lcd/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_lcd/interface", "/Users/<USER>/.platformio/packages/framework-espidf/components/protobuf-c/protobuf-c", "/Users/<USER>/.platformio/packages/framework-espidf/components/protocomm/include/common", "/Users/<USER>/.platformio/packages/framework-espidf/components/protocomm/include/security", "/Users/<USER>/.platformio/packages/framework-espidf/components/protocomm/include/transports", "/Users/<USER>/.platformio/packages/framework-espidf/components/protocomm/include/crypto/srp6a", "/Users/<USER>/.platformio/packages/framework-espidf/components/protocomm/proto-c", "/Users/<USER>/.platformio/packages/framework-espidf/components/esp_local_ctrl/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/espcoredump/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/espcoredump/include/port/xtensa", "/Users/<USER>/.platformio/packages/framework-espidf/components/wear_levelling/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/fatfs/diskio", "/Users/<USER>/.platformio/packages/framework-espidf/components/fatfs/src", "/Users/<USER>/.platformio/packages/framework-espidf/components/fatfs/vfs", "/Users/<USER>/.platformio/packages/framework-espidf/components/idf_test/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/idf_test/include/esp32", "/Users/<USER>/.platformio/packages/framework-espidf/components/ieee802154/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/json/cJSON", "/Users/<USER>/.platformio/packages/framework-espidf/components/mqtt/esp-mqtt/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/nvs_sec_provider/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/perfmon/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/rt/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/spiffs/include", "/Users/<USER>/.platformio/packages/framework-espidf/components/wifi_provisioning/include", "/Users/<USER>/ESPHome/outside-monitor/.esphome/build/outside-monitor-box/managed_components/espressif__mdns/include"], "compatlib": ["/Users/<USER>/ESPHome/outside-monitor/.esphome/build/outside-monitor-box/.piolibdeps/outside-monitor-box/ArduinoJson/src"], "toolchain": ["/Users/<USER>/.platformio/packages/toolchain-xtensa-esp-elf/xtensa-esp-elf/include/c++/14.2.0", "/Users/<USER>/.platformio/packages/toolchain-xtensa-esp-elf/xtensa-esp-elf/include/c++/14.2.0/xtensa-esp-elf", "/Users/<USER>/.platformio/packages/toolchain-xtensa-esp-elf/lib/gcc/xtensa-esp-elf/14.2.0/include", "/Users/<USER>/.platformio/packages/toolchain-xtensa-esp-elf/lib/gcc/xtensa-esp-elf/14.2.0/include-fixed", "/Users/<USER>/.platformio/packages/toolchain-xtensa-esp-elf/picolibc/include", "/Users/<USER>/.platformio/packages/toolchain-xtensa-esp-elf/xtensa-esp-elf/include"]}, "cc_flags": ["-<PERSON><PERSON>", "-Wno-enum-conversion", "-Wno-error=deprecated-declarations", "-Wno-error=extra", "-Wno-error=unused-but-set-variable", "-Wno-error=unused-function", "-Wno-error=unused-variable", "-Wno-frame-address", "-Wno-old-style-declaration", "-Wno-sign-compare", "-Wno-unused-parameter", "-fdata-sections", "-fdiagnostics-color=always", "-ffunction-sections", "-fmacro-prefix-map=/Users/<USER>/.platformio/packages/framework-espidf=/IDF", "-fmacro-prefix-map=/Users/<USER>/ESPHome/outside-monitor/.esphome/build/outside-monitor-box=.", "-fno-builtin-bzero", "-fno-builtin-memcpy", "-fno-builtin-memset", "-fno-builtin-stpcpy", "-fno-builtin-strncpy", "-fno-jump-tables", "-fno-tree-switch-conversion", "-freorder-blocks", "-fstrict-volatile-bitfields", "-gdwarf-4", "-ggdb", "-mlongcalls", "-std=gnu17", "-Wno-nonnull-compare", "-Wno-sign-compare", "-Wno-unused-but-set-variable", "-Wno-unused-variable", "-fno-exceptions"], "cxx_flags": ["-Wno-volatile", "-std=gnu++20", "-<PERSON><PERSON>", "-Wno-enum-conversion", "-Wno-error=deprecated-declarations", "-Wno-error=extra", "-Wno-error=unused-but-set-variable", "-Wno-error=unused-function", "-Wno-error=unused-variable", "-Wno-frame-address", "-Wno-sign-compare", "-Wno-unused-parameter", "-fdata-sections", "-fdiagnostics-color=always", "-ffunction-sections", "-fmacro-prefix-map=/Users/<USER>/.platformio/packages/framework-espidf=/IDF", "-fmacro-prefix-map=/Users/<USER>/ESPHome/outside-monitor/.esphome/build/outside-monitor-box=.", "-fno-builtin-bzero", "-fno-builtin-memcpy", "-fno-builtin-memset", "-fno-builtin-stpcpy", "-fno-builtin-strncpy", "-fno-exceptions", "-fno-jump-tables", "-fno-rtti", "-fno-tree-switch-conversion", "-freorder-blocks", "-fstrict-volatile-bitfields", "-fuse-cxa-atexit", "-gdwarf-4", "-ggdb", "-mlongcalls", "-Wno-nonnull-compare", "-Wno-sign-compare", "-Wno-unused-but-set-variable", "-Wno-unused-variable", "-fno-exceptions"], "cc_path": "/Users/<USER>/.platformio/packages/toolchain-xtensa-esp-elf/bin/xtensa-esp32-elf-gcc", "cxx_path": "/Users/<USER>/.platformio/packages/toolchain-xtensa-esp-elf/bin/xtensa-esp32-elf-g++", "gdb_path": "bin/xtensa-esp32-elf-gdb", "prog_path": "/Users/<USER>/ESPHome/outside-monitor/.esphome/build/outside-monitor-box/.pioenvs/outside-monitor-box/firmware.elf", "svd_path": null, "compiler_type": "gcc", "targets": [{"name": "menuconfig", "title": "<PERSON>", "description": null, "group": "Platform"}, {"name": "buildfs", "title": "Build Filesystem Image", "description": null, "group": "Platform"}, {"name": "size", "title": "Program Size", "description": "Calculate program size", "group": "Platform"}, {"name": "upload", "title": "Upload", "description": null, "group": "Platform"}, {"name": "uploadfs", "title": "Upload Filesystem Image", "description": null, "group": "Platform"}, {"name": "uploadfsota", "title": "Upload Filesystem Image OTA", "description": null, "group": "Platform"}, {"name": "erase_upload", "title": "Erase Flash and Upload", "description": null, "group": "Platform"}, {"name": "erase", "title": "Erase Flash", "description": null, "group": "Platform"}, {"name": "metrics", "title": "Firmware Size Metrics", "description": "Analyze firmware size using esp-idf-size (supports CLI args after --)", "group": "Custom"}, {"name": "metrics-only", "title": "Firmware Size Metrics (No Build)", "description": "Analyze firmware size without building first", "group": "Custom"}], "extra": {"flash_images": [{"offset": "0x1000", "path": "/Users/<USER>/ESPHome/outside-monitor/.esphome/build/outside-monitor-box/.pioenvs/outside-monitor-box/bootloader.bin"}, {"offset": "0x8000", "path": "/Users/<USER>/ESPHome/outside-monitor/.esphome/build/outside-monitor-box/.pioenvs/outside-monitor-box/partitions.bin"}, {"offset": "0x9000", "path": "/Users/<USER>/ESPHome/outside-monitor/.esphome/build/outside-monitor-box/.pioenvs/outside-monitor-box/ota_data_initial.bin"}], "application_offset": "0x0"}}